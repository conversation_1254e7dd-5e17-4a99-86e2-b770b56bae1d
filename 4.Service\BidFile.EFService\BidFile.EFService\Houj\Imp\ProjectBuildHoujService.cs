using BidFile.BaseEFCore;
using BidFile.Model;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetTopologySuite.Operation.Overlay;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using NetTopologySuite;
using NetTopologySuite.Geometries;
using NetTopologySuite.Algorithm;
using NPoco.Expressions;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace BidFile.EFService.Houj.Imp
{
    /// <summary>
    /// 单项工程后基础信息服务 - 提供构件指标计算和公式处理功能
    /// </summary>
    public class ProjectBuildHoujService : IProjectBuildHoujService
    {
        private readonly ILogger<ProjectBuildHoujService> _logger;
        private readonly Lazy<IProjecHoujViewInfoRepository> _projecHoujViewInfoRepository;
        private readonly Lazy<IQuotaRuleService> _quotaRuleService;
        private readonly CalcRuleContext _calcRuleContext;
        private readonly ProjectBuildHoujBaseContext _projectBuildHoujBaseContext;
        private readonly Lazy<IBaseTextAttributeRepository> _baseTextAttributeRepository;

        /// <summary>
        /// 初始化单项工程后基础信息服务
        /// </summary>
        /// <param name="logger">日志记录器</param>
        /// <param name="calcRuleContext">计算规则数据库上下文</param>
        /// <param name="projectBuildHoujContext">项目构件数据库上下文</param>
        /// <param name="projecHoujViewInfoRepository">构件视图信息仓库</param>
        /// <param name="quotaRuleService">指标规则服务</param>
        public ProjectBuildHoujService(
            ILogger<ProjectBuildHoujService> logger,
            CalcRuleContext calcRuleContext,
            ProjectBuildHoujBaseContext projectBuildHoujBaseContext,
            Lazy<IProjecHoujViewInfoRepository> projecHoujViewInfoRepository,
            Lazy<IQuotaRuleService> quotaRuleService,
            Lazy<IBaseTextAttributeRepository> baseTextAttributeRepository)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _calcRuleContext = calcRuleContext ?? throw new ArgumentNullException(nameof(calcRuleContext));
            _projectBuildHoujBaseContext = projectBuildHoujBaseContext ?? throw new ArgumentNullException(nameof(projectBuildHoujBaseContext));
            _projecHoujViewInfoRepository = projecHoujViewInfoRepository ?? throw new ArgumentNullException(nameof(projecHoujViewInfoRepository));
            _quotaRuleService = quotaRuleService ?? throw new ArgumentNullException(nameof(quotaRuleService));
            _baseTextAttributeRepository = baseTextAttributeRepository ?? throw new ArgumentNullException(nameof(baseTextAttributeRepository));
        }

        /// <summary>
        /// 获取所有单项工程列表
        /// </summary>
        /// <returns>单项工程列表</returns>
        public async Task<List<HoujBuild>> GetBuildList()
        {
            try
            {
                _logger.LogInformation("获取单项工程列表");
                return await _projectBuildHoujBaseContext.HoujBuild.AsNoTracking().ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取单项工程列表时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 获取所有单项工程的指标数据
        /// </summary>
        /// <returns>单项工程指标数据列表</returns>
        public async Task<List<BuildQuotaValue>> QuotaAllBuilds(long? buildingid = null,List<long>? speciltyids = null, List<string>? floornames = null, List<string>? xgj_entnames = null,List<long>? entobjdataids = null)
        {
            try
            {
                _logger.LogInformation("开始获取所有单项工程的指标数据");
                var builds = await GetBuildList();

                if (builds == null || builds.Count == 0)
                {
                    _logger.LogWarning("未找到单项工程数据");
                    return [];
                }
                if (buildingid.HasValue)
                {
                    builds = builds.Where(b => b.buildingid == buildingid).ToList();
                }
                var result = new List<BuildQuotaValue>();
                foreach (var build in builds)
                {
                    _logger.LogInformation($"处理单项工程: {build.buildname} (ID: {build.buildingid})");

                    var speciltys = await _projectBuildHoujBaseContext.HoujBuildSpecialty.Where(s => s.buildingid == build.buildingid && (speciltyids == null || speciltyids.Contains(s.speciltyid))).ToListAsync();
                    if (speciltys == null)
                    {
                        _logger.LogWarning($"未找到专业ID {build.buildingid} 的单项工程");
                        continue;
                    }
                    var buildQuotaValue = new BuildQuotaValue
                    {
                        buildingid = build.buildingid,
                        buildname = build.buildname,
                        quotavalues2 = []
                    };
                    foreach (var specilty in speciltys)
                    {
                        var quotaValues = await QuotaRuleExec(build.buildingid, specilty.tablenamesuffix, floornames, xgj_entnames,entobjdataids);
                        quotaValues.ForEach(q =>
                        {
                            var temp_quotaValue = buildQuotaValue.quotavalues2.FirstOrDefault(v => v?.quota_id == q?.quota_id);
                            if (temp_quotaValue != null)
                            {
                                temp_quotaValue.all_data.AddRange(q.all_data);
                            }
                            else
                            {
                                buildQuotaValue.quotavalues2.Add(q);
                            }
                        });
                    }
                    result.Add(buildQuotaValue);
                }

                _logger.LogInformation($"已完成 {result.Count} 个单项工程的指标数据获取");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有单项工程指标数据时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 获取所有单项工程的按指标类型分类的所有指标数据
        /// </summary>
        /// <param name="buildingid">单项工程 ID，可选，如果指定则只获取该单项工程的数据</param>
        /// <param name="storeyid">楼层 ID，可选</param>
        /// <param name="xgj_entnames">构件类型名称列表，可选</param>
        /// <returns>按单项工程分组的分类指标数据列表</returns>
        public async Task<List<BuildQuotaCategorizedAllValue>> GetAllBuildCategorizedAllData(long? buildingid = null, List<string>? floornames = null, List<string>? xgj_entnames = null)
        {
            try
            {
                // 构建日志消息，包含过滤条件信息
                var logMessage = buildingid.HasValue
                    ? $"开始获取单项工程 ID: {buildingid} 的分类指标数据"
                    : "开始获取所有单项工程的分类指标数据";
                _logger.LogInformation(logMessage);

                // 获取单项工程列表
                var builds = await GetBuildList();
                if (builds == null || builds.Count == 0)
                {
                    _logger.LogWarning("未找到单项工程数据");
                    return [];
                }

                // 如果指定了单项工程 ID，则过滤单项工程列表
                if (buildingid.HasValue)
                {
                    builds = builds.Where(b => b.buildingid == buildingid.Value).ToList();
                    if (builds.Count == 0)
                    {
                        _logger.LogWarning($"未找到 ID 为 {buildingid.Value} 的单项工程");
                        return [];
                    }
                }

                var result = new List<BuildQuotaCategorizedAllValue>();
                int processedBuilds = 0;

                // 遍历处理每个单项工程
                foreach (var build in builds)
                {
                    var speciltys = await _projectBuildHoujBaseContext.HoujBuildSpecialty.Where(s => s.buildingid == build.buildingid).ToListAsync();
                    if (speciltys == null)
                    {
                        _logger.LogWarning($"未找到专业ID {build.buildingid} 的单项工程");
                        continue;
                    }
                    var quotaValues = new List<QuotaValue2>();
                    foreach (var specilty in speciltys)
                    {
                        var temp_quotaValues = await QuotaRuleExec(build.buildingid, specilty.tablenamesuffix, floornames, xgj_entnames);
                        temp_quotaValues.ForEach(q =>
                        {
                            var temp_quotaValue = quotaValues.FirstOrDefault(v => v.quota_id == q.quota_id);
                            if (temp_quotaValue != null)
                            {
                                temp_quotaValue.all_data.AddRange(q.all_data);
                            }
                            else
                            {
                                quotaValues.Add(q);
                            }
                        });
                    }

                    // 创建分类数据对象
                    var categorizedData = new QuotaCategorizedAllData
                    {
                        CountTypeData = new List<QuotaCountAllData>(),
                        ComponentTypeData = new List<QuotaComponentAllData>()
                    };

                    // 处理数量类指标数据 (Type = 1)
                    var countTypeResults = quotaValues.Where(r => r.quota_type == 1).ToList();
                    foreach (var countResult in countTypeResults)
                    {
                        categorizedData.CountTypeData.Add(new QuotaCountAllData
                        {
                            QuotaId = countResult.quota_id,
                            QuotaTitle = countResult.quota_title,
                            QuotaType = countResult.quota_type,
                            QuotaRelationType = countResult.quota_relation_type,
                            ComponentName = countResult.component_name,
                            ResultIndex = countResult.result_index,
                            AllData = countResult.all_data
                        });
                    }

                    // 处理构件类指标数据 (Type = 2)
                    var componentTypeResults = quotaValues.Where(r => r.quota_type == 2).ToList();
                    foreach (var componentResult in componentTypeResults)
                    {
                        categorizedData.ComponentTypeData.Add(new QuotaComponentAllData
                        {
                            QuotaId = componentResult.quota_id,
                            QuotaTitle = componentResult.quota_title,
                            QuotaType = componentResult.quota_type,
                            QuotaRelationType = componentResult.quota_relation_type,
                            ComponentName = componentResult.component_name,
                            ResultIndex = componentResult.result_index,
                            AllData = componentResult.all_data
                        });
                    }

                    // 添加到结果列表
                    result.Add(new BuildQuotaCategorizedAllValue
                    {
                        BuildingId = build.buildingid,
                        BuildName = build.buildname,
                        QuotaData = categorizedData
                    });

                    processedBuilds++;
                }

                // 构建结果日志消息
                var resultLogMessage = buildingid.HasValue
                    ? $"单项工程 ID: {buildingid} 的分类指标数据获取完成"
                    : $"所有单项工程的分类指标数据获取完成，共处理 {processedBuilds} 个单项工程，有 {result.Count} 个单项工程存在指标数据";
                _logger.LogInformation(resultLogMessage);
                return result;
            }
            catch (Exception ex)
            {
                // 构建错误日志消息
                var errorMessage = buildingid.HasValue
                    ? $"获取单项工程 ID: {buildingid} 的分类指标数据时发生错误"
                    : "获取所有单项工程的分类指标数据时发生错误";
                _logger.LogError(ex, errorMessage);
                throw;
            }
        }

        /// <summary>
        /// 执行构件指标规则计算
        /// </summary>
        /// <param name="buildid">单项工程 ID</param>
        /// <returns>指标计算结果列表</returns>
        public async Task<List<QuotaValue2>> QuotaRuleExec(long buildid, string suffix, List<string>? floornames = null, List<string>? xgj_entnames = null, List<long>? entobjdataids = null, bool hasHard = false)
        {
            try
            {
                _logger.LogInformation($"开始执行单项工程 ID: {buildid} 的指标规则计算");

                // 获取所有指标规则
                var rules = await _quotaRuleService.Value.GetAllList();
                if (rules == null || rules.Count == 0)
                {
                    _logger.LogWarning("未找到指标规则数据");
                    return [];
                }

                var resultList = new List<QuotaValue2>();
                int processedRules = 0;

                // 遍历处理每一条规则
                foreach (var rule in rules)
                {
                    // 只处理自身关系类型的构件指标
                    if (rule.RelationType == 2)
                    {
                        var ruleResult = await ProcessRule(rule, suffix, floornames, xgj_entnames, entobjdataids, hasHard);
                        if (ruleResult != null)
                        {
                            resultList.Add(ruleResult);
                        }
                        processedRules++;
                    }
                }

                _logger.LogInformation($"单项工程 ID: {buildid} 的指标规则计算完成，共处理 {processedRules} 条规则，有效结果 {resultList.Count} 条");
                return resultList;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"执行单项工程 ID: {buildid} 的指标规则计算时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 处理单条指标规则
        /// </summary>
        /// <param name="rule">指标规则</param>
        /// <returns>指标计算结果，如果无效则返回 null</returns>
        private async Task<QuotaValue2?> ProcessRule(MeasureInfo rule, string suffix, List<string>? floornames = null, List<string>? xgj_entnames = null, List<long>? entobjdataids = null, bool hasHard = false)
        {
            try
            {
                // 解析规则对象
                var ruleObj = JsonSerializer.Deserialize<QuotaRuleObj>(rule.JsonData);
                if (ruleObj?.results == null || ruleObj.results.Count == 0)
                {
                    _logger.LogWarning($"规则 ID: {rule.Id} 的结果定义为空");
                    return null;
                }

                if (xgj_entnames?.Count > 0 && xgj_entnames?.Contains(ruleObj?.bcomsa?.FirstOrDefault()?.bcom) == false)
                {
                    return null;
                }
                if (entobjdataids?.Count > 0)
                {
                    var temp_xgj_entnames = await _projecHoujViewInfoRepository.Value.GetEntobjdataIdsXgjEntnames(suffix, entobjdataids);
                    if (temp_xgj_entnames?.Count > 0 && temp_xgj_entnames?.Contains(ruleObj?.bcomsa?.FirstOrDefault()?.bcom) == false)
                    {
                        return null;
                    }
                }


                // 获取第一个结果定义作为过滤器
                QuotaObj? filter = ruleObj.results[0];
                int result_index = 0;

                // 如果过滤器为空，跳过当前规则
                if (filter == null)
                {
                    _logger.LogWarning($"规则 ID: {rule.Id} 的过滤器为空");
                    return null;
                }

                // 根据规则类型处理
                if (rule.Type == 2) // 构件类型
                {
                    return await ProcessComponentRule(rule, ruleObj, filter, result_index, suffix, floornames, entobjdataids, hasHard);
                }
                else if (rule.Type == 1) // 数量类型
                {
                    return await ProcessCountRule(rule, ruleObj, filter, result_index, suffix, floornames, hasHard);
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理规则 ID: {rule.Id} 时发生错误");
                return null;
            }
        }

        /// <summary>
        /// 处理构件类型的指标规则
        /// </summary>
        private async Task<QuotaValue2?> ProcessComponentRule(MeasureInfo rule, QuotaRuleObj ruleObj, QuotaObj filter, int result_index, string suffix, List<string>? floornames = null, List<long>? entobjdataids = null, bool hasHard = false)
        {
            var bcomsa = ruleObj.bcomsa?.FirstOrDefault();
            if (bcomsa == null)
            {
                _logger.LogWarning($"规则 ID: {rule.Id} 的构件定义为空");
                return null;
            }

            // 获取构件指标数据
            var quotaData = await _projecHoujViewInfoRepository.Value.ComponentQuotaWithQuotaObj(filter, bcomsa, suffix, floornames,entobjdataids);
            if (hasHard)
            {
                quotaData = quotaData.Where(q => q.hard == true || q.noKeyValue == true || q.multiples == true).ToList();
            }

            // 检查是否有不符合指标的数据
            if (quotaData?.Any(q =>
                q.noKeyValue == true ||
                q.multiples == true ||
                q.fixedValues == true ||
                q.hard == true ||
                q.soft == true) == true)
            {
                _logger.LogInformation($"规则 Name: {rule.Title}({rule.Id}) 发现不符合指标的数据");
                var attrName = await _baseTextAttributeRepository.Value.GetAttrName(bcomsa.targetAttr);
                // 使用LINQ和投影创建新的数据集合，而不是直接修改原始集合
                var enhancedData = quotaData.Select(item =>
                {

                    // 创建新对象，保留原始属性
                    var newItem = new QuotaData2
                    {
                        entobjdataid = item.entobjdataid,
                        val = item.val,
                        noKeyValue = item.noKeyValue,
                        multiples = item.multiples,
                        fixedValues = item.fixedValues,
                        hard = item.hard,
                        soft = item.soft,
                        SBTK = item.SBTK,
                        ZWWZ = item.ZWWZ,
                        floorname = item.floorname,
                        xgj_entname = item.xgj_entname,
                        component_name = item.component_name,
                        quota_text = item.quota_text // 默认保留原始值
                    };
                    // 只为需要验证失败描述的数据生成描述文本
                    if (item.noKeyValue == true ||
                        item.multiples == true ||
                        item.fixedValues == true ||
                        item.hard == true ||
                        item.soft == true)
                    {
                        var description = new StringBuilder($"{item.xgj_entname}.{attrName}({item.val}) 不符合 {rule.Title} ");

                        // 添加具体的不符合原因
                        if (item.noKeyValue == true)
                        {
                            description.Append("缺少属性值 ");
                        }

                        if (item.hard == true)
                        {
                            var hardRange = GetRangeDescription(filter.result_hard_min, filter.result_hard_max);
                            description.Append($"硬指标{hardRange} ");
                        }

                        if (item.soft == true)
                        {
                            var softRange = GetRangeDescription(filter.result_soft_min, filter.result_soft_max);
                            description.Append($"软指标{softRange} ");
                        }

                        if (item.multiples == true)
                        {
                            description.Append($"不是{filter.multiples}的倍数 ");
                        }

                        if (item.fixedValues == true && filter.fixedValues?.Count > 0)
                        {
                            description.Append($"不在固定值列表({string.Join(",", filter.fixedValues)})中 ");
                        }

                        // 设置新对象的描述文本
                        newItem.quota_text = description.ToString().Trim();
                    }
                    return newItem;
                }).ToList();

                return new QuotaValue2
                {
                    all_data = enhancedData,
                    quota_id = rule.Id,
                    quota_title = rule.Title,
                    quota_type = rule.Type,
                    quota_relation_type = rule.RelationType,
                    result_index = result_index,
                    component_name = bcomsa.bcom,
                    specialty = bcomsa.bcomIds[0]
                };
            }

            return null;
        }

        /// <summary>
        /// 获取范围描述文本
        /// </summary>
        private static string GetRangeDescription(double? min, double? max)
        {
            if (min.HasValue && max.HasValue)
            {
                if (min.Value == max.Value)
                {
                    return $"(应为 {min.Value})";
                }
                return $"({min.Value}~{max.Value})";
            }
            else if (min.HasValue)
            {
                return $"(>{min.Value})";
            }
            else if (max.HasValue)
            {
                return $"(<{max.Value})";
            }

            return "";
        }

        /// <summary>
        /// 处理数量类型的指标规则
        /// </summary>
        private async Task<QuotaValue2?> ProcessCountRule(MeasureInfo rule, QuotaRuleObj ruleObj, QuotaObj filter, int result_index, string suffix, List<string>? floornames = null, bool hasHard = false)
        {
            var bcomsa = ruleObj.bcomsa?.FirstOrDefault();
            if (bcomsa == null)
            {
                _logger.LogWarning($"规则 ID: {rule.Id} 的构件定义为空");
                return null;
            }

            // 获取构件数量指标数据
            var quotaData = await _projecHoujViewInfoRepository.Value.ComponentCountQuotaWithQuotaObj(filter, bcomsa, suffix, floornames);
            if (hasHard)
            {
                quotaData = quotaData.Where(q => q.hard == true || q.soft == true).ToList();
            }
            if (quotaData != null)
            {
                _logger.LogInformation($"规则 {rule.Title}(ID: {rule.Id}) 数量指标计算完成");
                // 使用LINQ和投影创建新的数据集合，而不是直接修改原始集合
                var enhancedData = quotaData.Select(item =>
                {
                    // 创建新对象，保留原始属性
                    var newItem = new QuotaData2
                    {
                        val = item.val,
                        hard = item.hard,
                        soft = item.soft,
                        floorname = item.floorname,
                        quota_text = item.quota_text // 默认保留原始值
                    };

                    // 只为需要验证失败描述的数据生成描述文本
                    if (item.hard == true || item.soft == true)
                    {
                        var description = new StringBuilder($"{item.xgj_entname}({item.val}) 不符合 {rule.Title} ");

                        // 添加具体的不符合原因
                        if (item.hard == true)
                        {
                            var hardRange = GetRangeDescription(filter.result_hard_min, filter.result_hard_max);
                            description.Append($"数量硬指标{hardRange} ");
                        }

                        if (item.soft == true)
                        {
                            var softRange = GetRangeDescription(filter.result_soft_min, filter.result_soft_max);
                            description.Append($"数量软指标{softRange} ");
                        }
                        // 设置新对象的描述文本
                        newItem.quota_text = description.ToString().Trim();
                    }
                    return newItem;
                }).ToList();

                return new QuotaValue2
                {
                    all_data = enhancedData,
                    quota_type = rule.Type,
                    quota_relation_type = rule.RelationType,
                    quota_id = rule.Id,
                    quota_title = rule.Title,
                    result_index = result_index,
                    component_name = bcomsa.bcom,
                    specialty = bcomsa.bcomIds[0]
                };
            }

            return null;
        }

        /// <summary>
        /// 获取指定 ID 列表的构件数据
        /// </summary>
        /// <param name="ids">构件 ID 列表</param>
        /// <returns>构件视图信息列表</returns>
        public async Task<List<HoujViewInfo>> GetAllQuotaComponents(List<long> ids)
        {
            if (ids == null || ids.Count == 0)
            {
                _logger.LogWarning("构件 ID 列表为空");
                return [];
            }

            try
            {
                _logger.LogInformation($"获取 {ids.Count} 个构件的数据");
                return await _projecHoujViewInfoRepository.Value.GetAllQuotaComponents(ids);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取构件数据时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 获取按指标类型分类的验证失败数据
        /// </summary>
        /// <param name="buildid">单项工程 ID</param>
        /// <param name="storeyid">楼层 ID，可选</param>
        /// <param name="xgj_entnames">构件类型名称列表，可选</param>
        /// <returns>按指标类型分类的验证失败数据</returns>
        public async Task<BuildQuotaCategorizedValue> GetCategorizedQuotaFailures(long buildid, List<long>? speciltyids, List<string>? floornames = null, List<string>? xgj_entnames = null, List<long>? entobjdataids = null)
        {
            try
            {
                var specialtys = await _projectBuildHoujBaseContext.HoujBuildSpecialty.Where(s => s.buildingid == buildid).ToListAsync();
                if (specialtys == null)
                {
                    _logger.LogWarning($"未找到专业ID {buildid} 的单项工程");
                    return new BuildQuotaCategorizedValue();
                }
                if (speciltyids?.Count > 0)
                {
                    specialtys = [specialtys.FirstOrDefault(s => speciltyids.Contains(s.speciltyid))];
                }
                var fullResults = new List<QuotaValue2>();
                _logger.LogInformation($"开始获取单项工程 ID: {buildid} 的分类指标验证失败数据");
                foreach (var specilty in specialtys)
                {
                    var quotaValues = await QuotaRuleExec(buildid, specilty.tablenamesuffix, floornames, xgj_entnames, entobjdataids, false);
                    if (quotaValues?.Count > 0)
                    {
                        quotaValues.ToList().ForEach(q =>
                        {
                            var temp_quotaValue = fullResults.FirstOrDefault(v => v.quota_id == q.quota_id);
                            if (temp_quotaValue != null)
                            {
                                temp_quotaValue.all_data.AddRange(q.all_data);
                            }
                            else
                            {
                                fullResults.Add(q);
                            }
                        });
                    }
                }

                // 获取完整的指标验证结果
                if (fullResults == null || fullResults.Count == 0)
                {
                    _logger.LogInformation($"单项工程 ID: {buildid} 没有指标验证失败数据");
                    return new BuildQuotaCategorizedValue();
                }

                var result = new BuildQuotaCategorizedValue
                {
                    CountTypeData = new List<QuotaCountData>(),
                    ComponentTypeData = new List<QuotaComponentData>()
                };

                // 处理数量类指标数据 (Type = 1)
                var countTypeResults = fullResults.Where(r => r.quota_type == 1 && r.all_data?.Any() == true).ToList();
                foreach (var countResult in countTypeResults)
                {
                    var data = new QuotaCountData() { QuotaTitle = countResult.quota_title, ComponentName = countResult.component_name };
                    var validData = countResult.all_data
                        .Where(data => !string.IsNullOrEmpty(data.quota_text))
                        .Select(data => new QuotaCountDataItem
                        {
                            Count = data.val,
                            QuotaText = data.quota_text,
                            FloorName = data.floorname
                        });
                    data.CountData = validData.ToList();
                    result.CountTypeData.Add(data);
                }

                // 处理构件类指标数据 (Type = 2)
                var componentTypeResults = fullResults.Where(r => r.quota_type == 2 && r.all_data?.Any() == true).ToList();

                // 收集所有构件类数据，按 entobjdataid 分组
                var componentDataGroups = componentTypeResults
                    .SelectMany(r => r.all_data)
                    .Where(data => data.entobjdataid.HasValue && !string.IsNullOrEmpty(data.quota_text))
                    .GroupBy(data => data.entobjdataid.Value);

                // 处理每个构件组
                foreach (var group in componentDataGroups)
                {
                    result.ComponentTypeData.Add(new QuotaComponentData
                    {
                        EntObjDataId = group.Key,
                        QuotaData = group.Select(g => new QuotaDataItem
                        {
                            Val = g.val,
                            QuotaText = g.quota_text
                        })
                    });
                }

                _logger.LogInformation($"单项工程 ID: {buildid} 的分类指标验证失败数据获取完成，数量类: {result.CountTypeData.Count} 条，构件类: {result.ComponentTypeData.Count} 条");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取单项工程 ID: {buildid} 的分类指标验证失败数据时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 获取公式中用到的属性代码
        /// </summary>
        /// <returns>属性代码列表</returns>
        private async Task<List<string>> GetAttrCodes()
        {
            try
            {
                // 从视图中提取所有公式中使用的属性代码
                const string sql = "select distinct jt.value from view_calc_bcom_formula vf,json_table(settings->'$[*].fparams[*].attr_code','$[*]' COLUMNS(value varchar(255) path '$')) as jt";
                return await _calcRuleContext.Database.SqlQueryRaw<string>(sql).ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取属性代码时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 获取图元公式
        /// </summary>
        /// <returns>图元公式列表</returns>
        public async Task<List<EntObjDataFormula>> GetFormulas(long? speciltyid, List<long>? entobjdataids = null)
        {
            try
            {
                _logger.LogInformation("开始获取图元公式");

                // 获取所有公式定义
                var formulas = await _calcRuleContext.Set<ViewCalcBComFormula>().AsNoTracking().ToListAsync();
                if (!(formulas?.Count > 0))
                {
                    _logger.LogWarning("未找到公式定义");
                    return [];
                }

                // 获取公式中使用的属性代码
                var attrCodes = await GetAttrCodes();
                if (attrCodes == null || attrCodes.Count == 0)
                {
                    _logger.LogWarning("未找到公式中使用的属性代码");
                    return [];
                }

                attrCodes = [.. attrCodes ?? [], "起点", "终点"];

                // 获取具有数量标签的构件属性数据
                var list = await _projecHoujViewInfoRepository.Value.GetQuantityLableWithAttrs(attrCodes, speciltyid, entobjdataids);
                if (list == null || list.Count == 0)
                {
                    _logger.LogWarning("未找到具有数量标签的构件属性数据");
                    return [];
                }

                // 处理每个构件数据，生成公式
                var result = new List<EntObjDataFormula>();
                int processedCount = 0;

                foreach (var gdata in list.GroupBy(d => d.entobjdataid))
                {
                    foreach (var d in gdata)
                    {
                        var formula = formulas.FirstOrDefault(f => f.name == d.amountlabel);
                        // var attrName = await _baseTextAttributeRepository.Value.GetAttrName(d.attr);
                        var item = new EntObjDataFormulaItem
                        {
                            id = d.id,
                            name = d.name,
                            // attr = attrName,
                            attr = d.attrname,
                            iscalc = d.iscalc
                        };
                        //var sb = new StringBuilder($"{attrName} = ");
                        var sb = new StringBuilder();
                        var isOriginal = d.originalvalue == d.value;

                        if (formula != null)
                        {
                            sb.Append(GetFormula(d, formula));
                        }
                        else if (!isOriginal)
                        {
                            sb.Append($"{d.originalvalue}<原始量>");
                        }
                        if (!string.IsNullOrEmpty(d.deductdata))
                        {
                            sb.Append($" - {d.deductdata}");
                        }
                        if (d.additional.HasValue)
                        {
                            sb.Append($"{(d.additional > 0 ? " + " : " - ")}{d.additional.Value}<附加量>");
                        }
                        //sb.Append($" = {d.value}({d.unit})");

                        item.formula = sb.ToString();
                        result.Add(new EntObjDataFormula { entobjdataid = gdata.Key, formulas = [item] });
                        processedCount++;
                    }
                }

                _logger.LogInformation($"已处理 {processedCount} 个构件的公式，生成 {result.Count} 个公式结果");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取图元公式时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 替换公式内容 - 将公式中的参数替换为实际值
        /// </summary>
        /// <param name="entobjdata">构件属性数据</param>
        /// <param name="formula">公式定义</param>
        /// <returns>处理后的公式数据</returns>
        private string GetFormula(EntObjQuatityData entobjdata, ViewCalcBComFormula formula)
        {
            var result = string.Empty;
            try
            {
                // 解析公式设置
                var settingsArr = JsonSerializer.Deserialize<List<ViewCalcBComFormulaSettings>>(formula.settings);
                //按属性过滤
                var settings = settingsArr?.FirstOrDefault(s => s.attr_code == entobjdata.attr);
                if (settings == null)
                {
                    _logger.LogWarning($"公式 {formula.name} 的设置解析失败");
                }
                else
                {
                    result = settings.mathml;
                    foreach (var fparam in settings.fparams)
                    {
                        // 如果参数为空，则跳过
                        if (fparam == null || (string.IsNullOrEmpty(fparam.attr_code) && string.IsNullOrEmpty(fparam.name)))
                        {
                            continue;
                        }
                        // 根据属性代码处理
                        switch (fparam.attr_code ?? fparam.name)
                        {
                            // 顶标高/底标高处理
                            case "DBG":
                            case "DIBG":
                                result = ProcessElevationAttribute(result, entobjdata, fparam);
                                break;
                            //3D起点-终点长度
                            case "#CD":
                                var qd3d = entobjdata.attrs?.FirstOrDefault(a => a.name == "起点")?.value?.Split(',')?.Select(d => double.Parse(d))?.ToList();
                                var zd3d = entobjdata.attrs?.FirstOrDefault(a => a.name == "终点")?.value?.Split(',')?.Select(d => double.Parse(d))?.ToList();
                                var qddbg = entobjdata.attrs?.FirstOrDefault(a => a.name == "QDDBG")?.value;
                                var zddbg = entobjdata.attrs?.FirstOrDefault(a => a.name == "ZDDBG")?.value;
                                var qdbg = entobjdata.attrs?.FirstOrDefault(a => a.name == "QDDBG")?.value;
                                var zdbg = entobjdata.attrs?.FirstOrDefault(a => a.name == "ZDDBG")?.value;
                                var dbg = entobjdata.attrs?.FirstOrDefault(a => a.name == "DBG")?.value;
                                var dibg = entobjdata.attrs?.FirstOrDefault(a => a.name == "DIBG")?.value;

                                //起点-终点
                                if (qd3d?.Count == 2 && zd3d?.Count == 2)
                                {
                                    var startPoint = new Point(qd3d[0], qd3d[1]);
                                    var endPoint = new Point(zd3d[0], zd3d[1]);
                                    double? temp_qddbg = 0d;
                                    double? temp_zddbg = 0d;
                                    //起点-终点顶标高
                                    if (qddbg != null && zddbg != null && qddbg != zddbg)
                                    {
                                        var tempHeight = entobjdata.bottomlevel ?? 0;
                                        qddbg = qddbg.Replace("层顶标高", Math.Round(tempHeight, 3).ToString("0.###"));
                                        zddbg = zddbg.Replace("层顶标高", Math.Round(tempHeight, 3).ToString("0.###"));
                                        // 计算标高表达式
                                        temp_qddbg = GetTextMathResult(qddbg);
                                        temp_zddbg = GetTextMathResult(zddbg);

                                    }
                                    //起点-终点标高
                                    else if(qdbg!=null && zdbg!=null && qdbg!=zdbg){
                                        var tempHeight = entobjdata.bottomlevel ?? 0;
                                        qdbg = qdbg.Replace("层底标高", Math.Round(tempHeight, 3).ToString("0.###"));
                                        zdbg = zdbg.Replace("层底标高", Math.Round(tempHeight, 3).ToString("0.###"));
                                        temp_qddbg = GetTextMathResult(qdbg);
                                        temp_zddbg = GetTextMathResult(zdbg);

                                    }
                                    startPoint.Z = temp_qddbg ?? 0;
                                    endPoint.Z = temp_zddbg ?? 0;
                                    var length = Math.Round(startPoint.Distance(endPoint), 3) / 1000;//米
                                    result = result.Replace(fparam.name, length.ToString("0.###"));

                                }
                                else if (dbg != null && dibg != null && dbg != dibg)
                                {
                                    var tempHeight = entobjdata.bottomlevel ?? 0;
                                    dbg = dbg.Replace("层底标高", Math.Round(tempHeight, 3).ToString("0.###"));
                                    dibg = dibg.Replace("层底标高", Math.Round(tempHeight, 3).ToString("0.###"));
                                    var temp_dbg = GetTextMathResult(dbg);
                                    var temp_dibg = GetTextMathResult(dibg);
                                    var length = (temp_dbg ?? 0) - (temp_dibg ?? 0);
                                    result = result.Replace(fparam.name, length.ToString("0.###"));
                                }
                                else
                                {
                                    fparam.attr_code = "CD";
                                    result = ProcessDefaultAttribute(result, entobjdata, fparam);
                                }
                                break;
                            case "#CG":
                                result = result.Replace(fparam.name, entobjdata.height?.ToString("0.###") ?? "层高");
                                break;
                            case "#area":
                                result = result.Replace(fparam.name, entobjdata.attrs?.FirstOrDefault(a => a.name == "area")?.value ?? "截面面积");
                                break;
                            default:
                                // 其他属性直接替换
                                result = ProcessDefaultAttribute(result, entobjdata, fparam);
                                break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"处理公式 {formula.name} 时发生错误");
                result = string.Empty;
            }
            return result;
        }

        /// <summary>
        /// 处理标高属性
        /// </summary>
        private static string ProcessElevationAttribute(string data, EntObjQuatityData entobjdata, ViewCalcBComFormulaFParams fparam)
        {
            // 获取属性值
            var attrValue = entobjdata.attrs?.FirstOrDefault(a => a.name == fparam.attr_code)?.value;
            if (string.IsNullOrEmpty(attrValue))
            {
                return data;
            }
            var tempHeight = fparam.attr_code == "DBG" ? (entobjdata.bottomlevel ?? 0) + (entobjdata.height ?? 0) : entobjdata.bottomlevel ?? 0;
            var processedValue = attrValue.Replace(fparam.attr_code == "DBG" ? "层顶标高" : "层底标高", Math.Round(tempHeight, 3).ToString("0.###"));

            // 计算标高表达式
            var calculatedValue = GetTextMathResult(processedValue);

            // 替换公式中的参数
            return data.Replace(fparam.name, calculatedValue?.ToString() ?? processedValue);
        }


        /// <summary>
        /// 处理默认属性
        /// </summary>
        private static string ProcessDefaultAttribute(string data, EntObjQuatityData entobjdata, ViewCalcBComFormulaFParams fparam)
        {
            // 获取属性值
            var attrValue = entobjdata.attrs?.FirstOrDefault(a => a.name == fparam.attr_code)?.value;
            if (attrValue != null)
            {
                if (double.TryParse(attrValue, out double value))
                {
                    if (value > 0)
                    {
                        attrValue = (value / 1000).ToString("0.###");
                    }
                }
            }

            // 替换公式中的参数
            return data.Replace(fparam.name, attrValue ?? string.Empty);
        }

        /// <summary>
        /// 标高计算 - 处理简单的加减运算
        /// </summary>
        /// <param name="text">要计算的文本表达式</param>
        /// <returns>计算结果，如果无法计算则返回 null</returns>
        private static double? GetTextMathResult(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return null;
            }

            try
            {
                // 检查是否包含加减运算
                var isPlus = text.Contains('+');
                var isMinus = text.Contains('-');

                if (isPlus || isMinus)
                {
                    // 分割表达式
                    var separator = isPlus ? '+' : '-';
                    var parts = text.Split(separator);

                    // 确保有两个部分并且都可以转换为数字
                    if (parts.Length == 2 &&
                        double.TryParse(parts[0].Trim(), out double firstValue) &&
                        double.TryParse(parts[1].Trim(), out double secondValue))
                    {
                        // 根据运算符进行计算
                        return isPlus ? firstValue + secondValue : firstValue - secondValue;
                    }
                }
                else if (double.TryParse(text.Trim(), out double value))
                {
                    // 如果是纯数字，直接返回
                    return value;
                }

                return null;
            }
            catch
            {
                return null;
            }
        }

    }
}
